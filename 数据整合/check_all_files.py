#!/usr/bin/env python3
"""
检查所有WT文件的时间范围和数据情况
"""
import pandas as pd
from pathlib import Path

def check_all_files():
    """检查所有WT文件的时间范围"""
    print("=== 检查所有WT文件的时间范围 ===")
    
    files = [
        "/Users/<USER>/Desktop/数据整理/1deal/数据整合/2025-07-24-WT5.csv",
        "2025-07-24-WT2.csv", 
        "2025-07-24-WT3.csv",
        "2025-07-24-WT4.csv",
        "2025-07-24-WT5.csv",
        "2025-07-24-WT6.csv"
    ]
    
    for file in files:
        if Path(file).exists():
            try:
                df = pd.read_csv(file)
                time_col = 'Date_Time'
                
                print(f"\n文件: {file}")
                print(f"总行数: {len(df)}")
                print(f"列名: {list(df.columns)}")
                
                if time_col in df.columns:
                    print(f"时间范围: {df[time_col].min()} - {df[time_col].max()}")
                    
                    # 检查数据是否有效
                    data_cols = [col for col in df.columns if col != time_col]
                    if data_cols:
                        sample_data = df[data_cols].head(3)
                        print(f"前3行数据样本:")
                        for idx, row in sample_data.iterrows():
                            print(f"  {dict(row)}")
                else:
                    print(f"警告: 未找到时间列 {time_col}")
                    
            except Exception as e:
                print(f"错误: 无法读取文件 {file}: {str(e)}")
        else:
            print(f"文件不存在: {file}")

if __name__ == "__main__":
    check_all_files()
