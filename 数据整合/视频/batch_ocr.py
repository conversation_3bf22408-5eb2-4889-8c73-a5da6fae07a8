#!/usr/bin/env python3
"""
批量图片数字识别脚本
用于处理视频截图中的数字识别
"""

import cv2
import numpy as np
from PIL import Image
import pytesseract
import os
import re
import csv
from datetime import datetime
import argparse

# 配置 Tesseract 路径
pytesseract.pytesseract.tesseract_cmd = '/opt/homebrew/bin/tesseract'

def preprocess_image(image_path):
    """原始预处理方法"""
    image = cv2.imread(image_path)
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    thresh = cv2.adaptiveThreshold(
        blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY_INV, 11, 2
    )
    kernel = np.ones((2, 2), np.uint8)
    dilated = cv2.dilate(thresh, kernel, iterations=1)
    return dilated

def preprocess_for_digits_v2(image_path):
    """增强对比度的预处理方法"""
    image = cv2.imread(image_path)
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)
    _, thresh = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
    cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
    return cleaned

def preprocess_for_digits_v3(image_path):
    """边缘检测的预处理方法"""
    image = cv2.imread(image_path)
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)
    edges = cv2.Canny(blurred, 50, 150)
    kernel = np.ones((2, 2), np.uint8)
    dilated = cv2.dilate(edges, kernel, iterations=1)
    return dilated

def enhanced_digit_recognition(image_path):
    """增强的数字识别函数"""
    if not os.path.exists(image_path):
        return None
        
    # 尝试多种预处理方法
    methods = [
        preprocess_image,
        preprocess_for_digits_v2,
        preprocess_for_digits_v3
    ]
    
    # OCR配置选项
    configs = [
        '--psm 8 --oem 3 -c tessedit_char_whitelist=0123456789.',
        '--psm 7 --oem 3 -c tessedit_char_whitelist=0123456789.',
        '--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789.',
        '--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789.',
        '--psm 6 --oem 3'
    ]
    
    best_result = ""
    
    for method in methods:
        try:
            processed_image = method(image_path)
            pil_image = Image.fromarray(processed_image)
            
            for config in configs:
                try:
                    result = pytesseract.image_to_string(pil_image, config=config).strip()
                    # 提取数字
                    numbers = re.findall(r'\d+\.?\d*', result)
                    if numbers:
                        clean_result = ' '.join(numbers)
                        if len(clean_result) > len(best_result):
                            best_result = clean_result
                except:
                    continue
        except:
            continue
    
    return best_result if best_result else None

def batch_process_images(input_dir, output_file, start_index=0, end_index=None):
    """
    批量处理图片
    
    Args:
        input_dir: 输入图片目录
        output_file: 输出CSV文件路径
        start_index: 开始索引
        end_index: 结束索引（None表示处理所有）
    """
    
    # 获取所有jpg文件
    jpg_files = [f for f in os.listdir(input_dir) if f.lower().endswith('.jpg')]
    jpg_files.sort()
    
    if end_index is None:
        end_index = len(jpg_files)
    
    # 限制处理范围
    jpg_files = jpg_files[start_index:end_index]
    
    results = []
    successful_count = 0
    
    print(f"开始处理 {len(jpg_files)} 张图片...")
    print(f"输出文件: {output_file}")
    
    for i, filename in enumerate(jpg_files):
        image_path = os.path.join(input_dir, filename)
        
        try:
            result = enhanced_digit_recognition(image_path)
            
            if result:
                results.append({
                    'filename': filename,
                    'recognized_text': result,
                    'status': 'success'
                })
                successful_count += 1
                print(f"✓ {filename}: {result}")
            else:
                results.append({
                    'filename': filename,
                    'recognized_text': '',
                    'status': 'no_digits_found'
                })
                print(f"✗ {filename}: 未识别到数字")
                
        except Exception as e:
            results.append({
                'filename': filename,
                'recognized_text': '',
                'status': f'error: {str(e)}'
            })
            print(f"✗ {filename}: 错误 - {e}")
        
        # 每处理100张图片显示进度
        if (i + 1) % 100 == 0:
            print(f"已处理: {i + 1}/{len(jpg_files)} 张图片，成功: {successful_count}")
    
    # 保存结果到CSV文件
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['filename', 'recognized_text', 'status', 'timestamp']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for result in results:
            result['timestamp'] = datetime.now().isoformat()
            writer.writerow(result)
    
    print(f"\n处理完成!")
    print(f"总计: {len(jpg_files)} 张图片")
    print(f"成功识别: {successful_count} 张")
    print(f"成功率: {successful_count/len(jpg_files)*100:.1f}%")
    print(f"结果已保存到: {output_file}")

def main():
    parser = argparse.ArgumentParser(description='批量图片数字识别')
    parser.add_argument('--input_dir', default='/Users/<USER>/Desktop/数据整合/视频/7_25',
                        help='输入图片目录')
    parser.add_argument('--output_file', default='tension7_25.csv',
                        help='输出CSV文件名')
    parser.add_argument('--start', type=int, default=0,
                        help='开始索引')
    parser.add_argument('--end', type=int, default=None,
                        help='结束索引')
    parser.add_argument('--sample', type=int, default=None,
                        help='仅处理前N张图片（用于测试）')
    
    args = parser.parse_args()
    
    if args.sample:
        args.end = args.sample
    
    batch_process_images(args.input_dir, args.output_file, args.start, args.end)

if __name__ == "__main__":
    main()
