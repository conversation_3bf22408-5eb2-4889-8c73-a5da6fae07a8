# 图片数字识别系统

这是一个专门用于识别视频截图中数字的OCR系统，使用OpenCV和Tesseract实现。

## 功能特点

- **多种预处理方法**: 使用3种不同的图像预处理算法提高识别准确率
- **智能OCR配置**: 自动尝试多种Tesseract配置参数
- **批量处理**: 支持大规模图片批量处理
- **结果分析**: 自动生成详细的识别结果分析报告
- **可视化**: 生成识别结果的统计图表

## 系统要求

- Python 3.7+
- OpenCV (cv2)
- Tesseract OCR
- PIL/Pillow
- pandas
- matplotlib
- numpy

## 安装依赖

```bash
# 安装Tesseract (macOS)
brew install tesseract

# 安装Python依赖
pip install opencv-python pillow pytesseract pandas matplotlib numpy
```

## 文件说明

### 核心文件

1. **qwnc.py** - 基础OCR识别脚本
   - 包含多种图像预处理方法
   - 增强的数字识别函数
   - 测试代码

2. **batch_ocr.py** - 批量处理脚本
   - 批量处理大量图片
   - 支持断点续传
   - 输出CSV格式结果

3. **analyze_results.py** - 结果分析脚本
   - 统计识别成功率
   - 分析数字分布
   - 生成可视化图表

4. **process_all_images.py** - 完整处理流程
   - 一键完成批量处理和分析
   - 适合处理大规模数据

## 使用方法

### 1. 快速测试

```bash
# 测试几张图片的识别效果
python qwnc.py
```

### 2. 小批量处理

```bash
# 处理前50张图片
python batch_ocr.py --sample 50

# 分析结果
python analyze_results.py
```

### 3. 大规模批量处理

```bash
# 处理所有图片
python process_all_images.py

# 处理指定范围的图片
python process_all_images.py --start_from 1000 --max_images 2000

# 自定义输出文件名
python process_all_images.py --output_prefix my_results
```

### 4. 高级用法

```bash
# 指定输入目录
python batch_ocr.py --input_dir /path/to/images --output_file results.csv

# 处理特定范围
python batch_ocr.py --start 100 --end 500

# 分析特定结果文件
python analyze_results.py --input my_results.csv --output my_report.txt
```

## 输出文件

### CSV结果文件
包含以下字段：
- `filename`: 图片文件名
- `recognized_text`: 识别出的文字
- `status`: 识别状态 (success/no_digits_found/error)
- `timestamp`: 处理时间戳

### 分析报告
- 识别成功率统计
- 数字分布分析
- 识别模式分析
- 改进建议

### 可视化图表
- 识别状态分布饼图
- 累积成功率趋势
- 数字分布直方图
- 文本长度分布

## 识别效果

基于测试结果：
- **成功率**: 72% (50张测试图片)
- **识别数字范围**: 2.0 - 196.0
- **主要识别模式**: 以点结尾的数字 (63.9%)

## 优化建议

1. **提高成功率**:
   - 调整图像预处理参数
   - 增加更多预处理方法
   - 优化Tesseract配置

2. **处理速度**:
   - 使用多进程并行处理
   - 优化图像读取和预处理
   - 缓存预处理结果

3. **识别准确性**:
   - 针对特定数字字体训练模型
   - 使用深度学习OCR方法
   - 结合图像分割技术

## 故障排除

### 常见问题

1. **Tesseract路径错误**
   ```python
   # 在脚本中修改Tesseract路径
   pytesseract.pytesseract.tesseract_cmd = '/your/path/to/tesseract'
   ```

2. **内存不足**
   ```bash
   # 减少批处理大小
   python batch_ocr.py --batch_size 100
   ```

3. **识别效果差**
   - 检查图片质量
   - 调整预处理参数
   - 尝试不同的OCR配置

## 扩展功能

可以考虑添加的功能：
- 支持更多图片格式
- 实时视频流处理
- Web界面
- API服务
- 数据库存储
- 结果导出到Excel

## 许可证

MIT License

## 更新日志

- v1.0: 基础OCR识别功能
- v1.1: 增加批量处理
- v1.2: 添加结果分析和可视化
- v1.3: 完整处理流程集成
