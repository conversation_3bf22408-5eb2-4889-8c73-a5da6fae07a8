# import os
# import cv2
# import numpy as np
# from PIL import Image
# import pytesseract
# from concurrent.futures import ThreadPoolExecutor, as_completed
# import time

# # --- 配置部分 ---
# # 1. 配置 Tesseract 路径 (根据你的系统修改)
# # macOS/Linux 示例:
# pytesseract.pytesseract.tesseract_cmd = '/opt/homebrew/bin/tesseract' # macOS Apple Silicon
# # pytesseract.pytesseract.tesseract_cmd = '/usr/bin/tesseract' # Linux / macOS Intel
# # Windows 示例:
# # pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# # 2. 图像文件夹和输出文件
# IMAGE_FOLDER = "img"  # 存放图片的文件夹
# OUTPUT_FILE = "results.txt" # 输出结果的文件

# # 3. Tesseract OCR 配置
# # --psm 7: 将图像视为单个文本行
# # --oem 3: 使用 LSTM OCR 引擎
# # -c tessedit_char_whitelist=0123456789.: 只识别数字和小数点
# TESSERACT_CONFIG = '--psm 7 --oem 3 -c tessedit_char_whitelist=0123456789.'

# # 4. 并行处理线程数
# MAX_WORKERS = os.cpu_count() # 使用 CPU 核心数作为线程数



# # --- 图像处理函数 ---
# # def preprocess_image_for_digits(image_path):
# #     """
# #     针对数字显示屏优化的图像预处理函数。
# #     """
# #     try:
# #         # 1. 读取图像
# #         image = cv2.imread(image_path)
# #         if image is None:
# #             print(f"警告: 无法读取图片 {image_path}")
# #             return None

# #         # 2. 转换为灰度图
# #         gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

# #         # 3. 高斯模糊去噪
# #         blurred = cv2.GaussianBlur(gray, (5, 5), 0)

# #         # 4. 自适应阈值化 (对于光照不均的图像效果好)
# #         # 或者使用固定阈值: _, thresh = cv2.threshold(blurred, 127, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
# #         thresh = cv2.adaptiveThreshold(
# #             blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
# #         )

# #         # 5. 反转颜色 (如果数字是暗色，背景是亮色)
# #         # 如果数字是亮色，背景是暗色，则跳过此步
# #         # inverted = cv2.bitwise_not(thresh)

# #         # 6. 形态学操作 (可选，用于连接断开的笔画或去除小噪点)
# #         # kernel = np.ones((2,2), np.uint8)
# #         # morphed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel) # 闭运算连接笔画
# #         # morphed = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel) # 开运算去噪点

# #         # 7. ROI 裁剪 (如果数字区域固定且已知)
# #         # 假设数字在图片中心 60% 区域
# #         h, w = thresh.shape
# #         roi = thresh[int(h*0.2):int(h*0.8), int(w*0.2):int(w*0.8)]
        
# #         # 或者根据你的图片手动调整裁剪区域
# #         # roi = thresh[y1:y2, x1:x2] 

# #         return roi # 或者返回 thresh 如果不裁剪

# #     except Exception as e:
# #         print(f"预处理图片 {image_path} 时出错: {e}")
# #         return None

# def preprocess_image_for_digits(image_path):
#     try:
#         # 读取图像
#         image = cv2.imread(image_path)
#         if image is None:
#             print(f"警告: 无法读取图片 {image_path}")
#             return None

#         # 转换为灰度图
#         gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

#         # 裁剪 ROI (假设数字在图片中间 60% 区域)
#         h, w = gray.shape
#         roi = gray[int(h * 0.2):int(h * 0.8), int(w * 0.2):int(w * 0.8)]

#         # 或者根据你的图片手动调整裁剪区域
#         # roi = gray[y1:y2, x1:x2]

#         # 高斯模糊去噪
#         blurred = cv2.GaussianBlur(gray, (5, 5), 0)  # 可以尝试调整核大小

#         return roi
#     except Exception as e:
#         print(f"预处理图片 {image_path} 时出错: {e}")
#         return None

# # --- OCR 识别函数 ---
# def recognize_digits(image_path):
#     """
#     对单张图片进行 OCR 识别。
#     """
#     try:
#         # 1. 预处理图像
#         processed_image = preprocess_image_for_digits(image_path)
#         if processed_image is None:
#             return image_path, "", "预处理失败"

#         # 2. 转换为 PIL Image
#         pil_image = Image.fromarray(processed_image)

#         # 3. OCR 识别
#         raw_text = pytesseract.image_to_string(pil_image, config=TESSERACT_CONFIG)

#         # 4. 清理识别结果
#         cleaned_text = raw_text.strip().replace(' ', '') # 去除首尾空格和中间空格

#         # 5. 基本验证 (可选)
#         # 检查是否只包含数字和小数点
#         # import re
#         # if re.fullmatch(r'\d+\.?\d*', cleaned_text):
#         #     return image_path, cleaned_text, "成功"
#         # else:
#         #     return image_path, cleaned_text, "识别结果可能不准确"

#         return image_path, cleaned_text, "成功"

#     except Exception as e:
#         return image_path, "", f"识别出错: {e}"

# # --- 主处理流程 ---
# def process_images_in_folder(folder_path, output_file, max_workers=4):
#     """
#     处理文件夹中的所有图片，并将结果写入文件。
#     """
#     print(f"开始处理文件夹 '{folder_path}' 中的图片...")
#     start_time = time.time()

#     # 1. 获取所有图片文件路径
#     image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff')
#     image_files = [
#         os.path.join(folder_path, f) for f in os.listdir(folder_path)
#         if f.lower().endswith(image_extensions)
#     ]
    
#     if not image_files:
#         print(f"文件夹 '{folder_path}' 中没有找到图片文件。")
#         return

#     print(f"共找到 {len(image_files)} 张图片。")

#     results = []
    
#     # 2. 使用线程池并行处理
#     with ThreadPoolExecutor(max_workers=max_workers) as executor:
#         # 提交所有任务
#         future_to_image = {executor.submit(recognize_digits, img_path): img_path for img_path in image_files}
        
#         # 获取结果
#         for future in as_completed(future_to_image):
#             image_path, text, status = future.result()
#             filename = os.path.basename(image_path)
#             results.append((filename, text, status))
#             print(f"已处理: {filename} -> '{text}' ({status})")

#     # 3. 按文件名排序结果 (可选，使输出更有序)
#     results.sort(key=lambda x: x[0])

#     # 4. 写入结果到文件
#     try:
#         with open(output_file, 'w', encoding='utf-8') as f:
#             f.write(f"OCR 处理结果\n")
#             f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
#             f.write("-" * 30 + "\n")
#             for filename, text, status in results:
#                 f.write(f"{filename}: {text} ({status})\n")
#         print(f"\n所有结果已保存到 '{output_file}'")
#     except Exception as e:
#         print(f"写入结果文件时出错: {e}")

#     end_time = time.time()
#     print(f"\n处理完成！总耗时: {end_time - start_time:.2f} 秒")

# # --- 程序入口 ---
# if __name__ == "__main__":
#     process_images_in_folder(IMAGE_FOLDER, OUTPUT_FILE, MAX_WORKERS)



import os
import cv2
import numpy as np
from PIL import Image, ImageEnhance
import pytesseract
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# --- 配置部分 ---
pytesseract.pytesseract.tesseract_cmd = '/opt/homebrew/bin/tesseract'  # 根据系统修改路径
IMAGE_FOLDER = "img"
OUTPUT_FILE = "ocr.txt"
DEBUG_FOLDER = "debug_images"  # 保存调试图像的文件夹
MAX_WORKERS = os.cpu_count()

# 创建调试文件夹
if not os.path.exists(DEBUG_FOLDER):
    os.makedirs(DEBUG_FOLDER)

def save_debug_image(image, filename, step_name):
    """保存调试图像"""
    debug_path = os.path.join(DEBUG_FOLDER, f"{filename}_{step_name}.jpg")
    cv2.imwrite(debug_path, image)

def preprocess_image_for_digits(image_path, save_debug=True):
    try:
        filename = os.path.splitext(os.path.basename(image_path))[0]

        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"警告: 无法读取图片 {image_path}")
            return None

        if save_debug:
            save_debug_image(image, filename, "01_original")

        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        if save_debug:
            save_debug_image(gray, filename, "02_gray")

        # 图像增强 - 增加对比度
        enhanced = cv2.convertScaleAbs(gray, alpha=1.5, beta=30)
        if save_debug:
            save_debug_image(enhanced, filename, "03_enhanced")

        # 尝试多种预处理方法
        processed_images = []

        # 方法1: 不裁剪，直接处理整个图像
        method1 = process_method_1(enhanced, filename, save_debug)
        if method1 is not None:
            processed_images.append(("method1", method1))

        # 方法2: 裁剪ROI后处理
        method2 = process_method_2(enhanced, filename, save_debug)
        if method2 is not None:
            processed_images.append(("method2", method2))

        # 方法3: 更激进的预处理
        method3 = process_method_3(enhanced, filename, save_debug)
        if method3 is not None:
            processed_images.append(("method3", method3))

        return processed_images

    except Exception as e:
        print(f"预处理图片 {image_path} 时出错: {e}")
        return None

def process_method_1(gray, filename, save_debug):
    """方法1: 直接处理整个图像"""
    try:
        # 高斯模糊
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)

        # OTSU阈值化
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # 反转图像（如果数字是暗色）
        inverted = cv2.bitwise_not(thresh)

        if save_debug:
            save_debug_image(inverted, filename, "04_method1")

        return inverted
    except:
        return None

def process_method_2(gray, filename, save_debug):
    """方法2: ROI裁剪 + 自适应阈值"""
    try:
        # 裁剪ROI
        h, w = gray.shape
        roi = gray[int(h * 0.1):int(h * 0.9), int(w * 0.1):int(w * 0.9)]

        # 高斯模糊
        blurred = cv2.GaussianBlur(roi, (5, 5), 0)

        # 自适应阈值化
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, 5
        )

        # 形态学操作
        kernel = np.ones((2, 2), np.uint8)
        morphed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

        if save_debug:
            save_debug_image(morphed, filename, "05_method2")

        return morphed
    except:
        return None

def process_method_3(gray, filename, save_debug):
    """方法3: 更激进的预处理"""
    try:
        # 直方图均衡化
        equalized = cv2.equalizeHist(gray)

        # 双边滤波去噪
        filtered = cv2.bilateralFilter(equalized, 9, 75, 75)

        # 固定阈值
        _, thresh = cv2.threshold(filtered, 127, 255, cv2.THRESH_BINARY)

        # 更大的形态学操作
        kernel = np.ones((3, 3), np.uint8)
        morphed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        morphed = cv2.morphologyEx(morphed, cv2.MORPH_OPEN, kernel)

        if save_debug:
            save_debug_image(morphed, filename, "06_method3")

        return morphed
    except:
        return None

def recognize_digits(image_path):
    try:
        # 预处理图像 - 获取多种处理方法的结果
        processed_images = preprocess_image_for_digits(image_path)
        if processed_images is None or len(processed_images) == 0:
            return image_path, "", "预处理失败"

        # 尝试不同的 Tesseract 配置
        configs = [
            '--psm 8 --oem 3 -c tessedit_char_whitelist=0123456789.',  # 单词模式
            '--psm 7 --oem 3 -c tessedit_char_whitelist=0123456789.',  # 单行模式
            '--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789.',  # 块模式
            '--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789.', # 原始行模式
            '--psm 8 --oem 3',  # 不限制字符
            '--psm 7 --oem 3',  # 不限制字符
        ]

        best_result = ""
        best_confidence = 0
        best_method = ""

        # 对每种预处理方法和每种配置进行尝试
        for method_name, processed_image in processed_images:
            # 图像缩放 - 有时放大图像可以提高识别率
            for scale_factor in [1.0, 2.0, 3.0]:
                if scale_factor != 1.0:
                    h, w = processed_image.shape
                    scaled_image = cv2.resize(processed_image, (int(w * scale_factor), int(h * scale_factor)), interpolation=cv2.INTER_CUBIC)
                else:
                    scaled_image = processed_image

                # 转换为 PIL Image
                pil_image = Image.fromarray(scaled_image)

                for config in configs:
                    try:
                        # OCR 识别
                        raw_text = pytesseract.image_to_string(pil_image, config=config)
                        initial_cleaned = raw_text.strip().replace(' ', '').replace('\n', '')

                        # 进一步清理结果
                        cleaned_text = clean_ocr_result(initial_cleaned)

                        # 计算置信度
                        confidence = calculate_confidence(initial_cleaned)

                        if confidence > best_confidence and len(cleaned_text) > 0:
                            best_result = cleaned_text
                            best_confidence = confidence
                            best_method = f"{method_name}_scale{scale_factor}_{config.split()[1]}"

                    except Exception as e:
                        continue

        if best_result:
            return image_path, best_result, f"成功 ({best_method})"
        else:
            return image_path, "", "未识别到内容"

    except Exception as e:
        return image_path, "", f"识别出错: {e}"

def clean_ocr_result(text):
    """清理OCR识别结果"""
    if not text:
        return ""

    # 使用正则表达式处理文本

    # 如果结果包含数字，尝试提取数字部分
    import re

    # 查找数字模式
    number_patterns = [
        r'\d+\.\d+',  # 小数
        r'\d+',       # 整数
        r'\.\d+',     # 以小数点开头的数字
    ]

    for pattern in number_patterns:
        matches = re.findall(pattern, text)
        if matches:
            # 返回最长的匹配
            longest_match = max(matches, key=len)
            return longest_match

    # 如果没有找到数字模式，尝试清理噪声
    # 移除前后的非数字字符
    cleaned = re.sub(r'^[^\d.]+', '', text)  # 移除开头的非数字字符
    cleaned = re.sub(r'[^\d.]+$', '', cleaned)  # 移除结尾的非数字字符

    # 如果结果只是小数点，在某些情况下保留
    if cleaned == '.':
        # 如果原始文本很短且主要是小数点，可能确实是小数点
        if len(text) <= 3 and text.count('.') >= 1:
            return "."
        return ""

    return cleaned

def calculate_confidence(text):
    """计算识别结果的置信度"""
    if not text:
        return 0

    # 先清理文本
    cleaned_text = clean_ocr_result(text)

    # 基本的置信度计算
    confidence = 0

    # 长度奖励（但不要太长）
    if 1 <= len(cleaned_text) <= 10:
        confidence += len(cleaned_text) * 15

    # 数字字符奖励
    digit_count = sum(1 for c in cleaned_text if c.isdigit())
    confidence += digit_count * 30

    # 小数点奖励（但只能有一个）
    dot_count = cleaned_text.count('.')
    if dot_count == 1:
        confidence += 20
    elif dot_count > 1:
        confidence -= 50  # 多个小数点是错误的

    # 惩罚非数字非小数点字符
    invalid_chars = sum(1 for c in cleaned_text if not (c.isdigit() or c == '.'))
    confidence -= invalid_chars * 100

    # 检查是否是合理的数字格式
    try:
        if cleaned_text and cleaned_text != '.':
            float(cleaned_text)
            confidence += 100  # 可以转换为浮点数的额外奖励
    except:
        confidence -= 50

    # 原始文本和清理后文本的差异惩罚
    if len(text) != len(cleaned_text):
        noise_ratio = (len(text) - len(cleaned_text)) / len(text)
        confidence -= noise_ratio * 50

    return max(0, confidence)

def process_images_in_folder(folder_path, output_file, max_workers=4):
    print(f"开始处理文件夹 '{folder_path}' 中的图片...")
    print(f"调试图像将保存到 '{DEBUG_FOLDER}' 文件夹")
    start_time = time.time()

    image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff')
    image_files = [
        os.path.join(folder_path, f) for f in os.listdir(folder_path)
        if f.lower().endswith(image_extensions)
    ]

    if not image_files:
        print(f"文件夹 '{folder_path}' 中没有找到图片文件。")
        return

    print(f"共找到 {len(image_files)} 张图片。")

    results = []
    successful_count = 0

    # 为了更好的调试，先处理单张图片
    print("\n开始逐张处理图片...")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_image = {executor.submit(recognize_digits, img_path): img_path for img_path in image_files}

        for future in as_completed(future_to_image):
            image_path, text, status = future.result()
            filename = os.path.basename(image_path)
            results.append((filename, text, status))

            if text and text.strip():
                successful_count += 1
                print(f"✓ {filename} -> '{text}' ({status})")
            else:
                print(f"✗ {filename} -> 未识别 ({status})")

    results.sort(key=lambda x: x[0])

    # 统计信息
    print(f"\n识别统计:")
    print(f"成功识别: {successful_count}/{len(image_files)} ({successful_count/len(image_files)*100:.1f}%)")

    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"OCR 处理结果\n")
            f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"成功识别: {successful_count}/{len(image_files)} ({successful_count/len(image_files)*100:.1f}%)\n")
            f.write("-" * 50 + "\n")
            for filename, text, status in results:
                f.write(f"{filename}: {text} ({status})\n")
        print(f"\n所有结果已保存到 '{output_file}'")
        print(f"调试图像已保存到 '{DEBUG_FOLDER}' 文件夹，可以查看预处理效果")
    except Exception as e:
        print(f"写入结果文件时出错: {e}")

    end_time = time.time()
    print(f"\n处理完成！总耗时: {end_time - start_time:.2f} 秒")

if __name__ == "__main__":
    process_images_in_folder(IMAGE_FOLDER, OUTPUT_FILE, MAX_WORKERS)