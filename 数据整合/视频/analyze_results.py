#!/usr/bin/env python3
"""
分析OCR识别结果
"""

import pandas as pd
import re
import matplotlib.pyplot as plt
from collections import Counter
import numpy as np

def load_and_analyze_results(csv_file):
    """加载并分析OCR结果"""
    
    # 读取CSV文件
    df = pd.read_csv(csv_file)
    
    print("=== OCR识别结果分析 ===")
    print(f"总图片数: {len(df)}")
    
    # 统计识别状态
    status_counts = df['status'].value_counts()
    print(f"\n识别状态统计:")
    for status, count in status_counts.items():
        percentage = count / len(df) * 100
        print(f"  {status}: {count} ({percentage:.1f}%)")
    
    # 分析成功识别的结果
    successful_df = df[df['status'] == 'success'].copy()
    print(f"\n成功识别的图片数: {len(successful_df)}")
    
    if len(successful_df) > 0:
        # 提取所有数字
        all_numbers = []
        for text in successful_df['recognized_text']:
            if pd.notna(text):
                # 提取所有数字（包括小数）
                numbers = re.findall(r'\d+\.?\d*', str(text))
                all_numbers.extend([float(n.rstrip('.')) for n in numbers if n])
        
        if all_numbers:
            print(f"\n数字统计:")
            print(f"  识别到的数字总数: {len(all_numbers)}")
            print(f"  数字范围: {min(all_numbers):.1f} - {max(all_numbers):.1f}")
            print(f"  平均值: {np.mean(all_numbers):.1f}")
            print(f"  中位数: {np.median(all_numbers):.1f}")
            
            # 数字分布统计
            print(f"\n数字分布:")
            ranges = [
                (0, 10, "0-10"),
                (10, 50, "10-50"),
                (50, 100, "50-100"),
                (100, 200, "100-200"),
                (200, float('inf'), "200+")
            ]
            
            for min_val, max_val, label in ranges:
                count = sum(1 for n in all_numbers if min_val <= n < max_val)
                if count > 0:
                    percentage = count / len(all_numbers) * 100
                    print(f"  {label}: {count} ({percentage:.1f}%)")
        
        # 分析识别文本模式
        print(f"\n识别文本模式分析:")
        text_patterns = []
        for text in successful_df['recognized_text']:
            if pd.notna(text):
                text = str(text).strip()
                if '.' in text and not text.endswith('.'):
                    text_patterns.append("包含小数点")
                elif text.endswith('.'):
                    text_patterns.append("以点结尾")
                elif ' ' in text:
                    text_patterns.append("包含空格")
                else:
                    text_patterns.append("纯数字")
        
        pattern_counts = Counter(text_patterns)
        for pattern, count in pattern_counts.most_common():
            percentage = count / len(text_patterns) * 100
            print(f"  {pattern}: {count} ({percentage:.1f}%)")
    
    return df, successful_df, all_numbers if 'all_numbers' in locals() else []

def create_visualizations(df, all_numbers, output_prefix="ocr_analysis"):
    """创建可视化图表"""
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 1. 识别状态饼图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    status_counts = df['status'].value_counts()
    axes[0, 0].pie(status_counts.values, labels=status_counts.index, autopct='%1.1f%%')
    axes[0, 0].set_title('识别状态分布')
    
    # 2. 成功率趋势（按文件名顺序）
    df_sorted = df.sort_values('filename')
    df_sorted['success'] = (df_sorted['status'] == 'success').astype(int)
    df_sorted['cumulative_success_rate'] = df_sorted['success'].expanding().mean() * 100
    
    axes[0, 1].plot(range(len(df_sorted)), df_sorted['cumulative_success_rate'])
    axes[0, 1].set_title('累积成功率趋势')
    axes[0, 1].set_xlabel('图片序号')
    axes[0, 1].set_ylabel('成功率 (%)')
    axes[0, 1].grid(True)
    
    # 3. 数字分布直方图
    if all_numbers:
        axes[1, 0].hist(all_numbers, bins=20, alpha=0.7, edgecolor='black')
        axes[1, 0].set_title('识别数字分布')
        axes[1, 0].set_xlabel('数字值')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 识别文本长度分布
    successful_df = df[df['status'] == 'success']
    if len(successful_df) > 0:
        text_lengths = [len(str(text)) for text in successful_df['recognized_text'] if pd.notna(text)]
        axes[1, 1].hist(text_lengths, bins=10, alpha=0.7, edgecolor='black')
        axes[1, 1].set_title('识别文本长度分布')
        axes[1, 1].set_xlabel('文本长度')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{output_prefix}_charts.png', dpi=300, bbox_inches='tight')
    print(f"\n图表已保存为: {output_prefix}_charts.png")
    
    return fig

def generate_report(csv_file, output_file="ocr_analysis_report.txt"):
    """生成分析报告"""
    
    df, successful_df, all_numbers = load_and_analyze_results(csv_file)
    
    # 生成详细报告
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("OCR识别结果分析报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"分析时间: {pd.Timestamp.now()}\n")
        f.write(f"数据文件: {csv_file}\n\n")
        
        f.write("1. 总体统计\n")
        f.write("-" * 20 + "\n")
        f.write(f"总图片数: {len(df)}\n")
        
        status_counts = df['status'].value_counts()
        for status, count in status_counts.items():
            percentage = count / len(df) * 100
            f.write(f"{status}: {count} ({percentage:.1f}%)\n")
        
        f.write(f"\n2. 成功识别分析\n")
        f.write("-" * 20 + "\n")
        f.write(f"成功识别图片数: {len(successful_df)}\n")
        
        if all_numbers:
            f.write(f"识别到的数字总数: {len(all_numbers)}\n")
            f.write(f"数字范围: {min(all_numbers):.1f} - {max(all_numbers):.1f}\n")
            f.write(f"平均值: {np.mean(all_numbers):.1f}\n")
            f.write(f"中位数: {np.median(all_numbers):.1f}\n")
        
        f.write(f"\n3. 识别样例\n")
        f.write("-" * 20 + "\n")
        sample_results = successful_df.head(10)
        for _, row in sample_results.iterrows():
            f.write(f"{row['filename']}: {row['recognized_text']}\n")
        
        f.write(f"\n4. 建议\n")
        f.write("-" * 20 + "\n")
        success_rate = len(successful_df) / len(df) * 100
        if success_rate >= 70:
            f.write("✓ 识别效果良好，可以进行大规模批量处理\n")
        elif success_rate >= 50:
            f.write("⚠ 识别效果中等，建议优化预处理参数\n")
        else:
            f.write("✗ 识别效果较差，需要调整识别策略\n")
    
    print(f"\n详细报告已保存为: {output_file}")
    
    # 创建可视化图表
    create_visualizations(df, all_numbers)
    
    return df

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='分析OCR识别结果')
    parser.add_argument('--input', default='ocr_results.csv', help='输入CSV文件')
    parser.add_argument('--output', default='ocr_analysis_report.txt', help='输出报告文件')
    
    args = parser.parse_args()
    
    try:
        generate_report(args.input, args.output)
    except FileNotFoundError:
        print(f"错误: 找不到文件 {args.input}")
        print("请先运行 batch_ocr.py 生成识别结果")
    except Exception as e:
        print(f"分析过程中出错: {e}")

if __name__ == "__main__":
    main()
