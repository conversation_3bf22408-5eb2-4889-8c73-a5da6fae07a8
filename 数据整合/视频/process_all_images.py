#!/usr/bin/env python3
"""
完整的图片数字识别处理脚本
处理所有图片并生成完整的分析报告
"""

import os
import sys
import time
from datetime import datetime
import argparse

# 导入我们的模块
from batch_ocr import batch_process_images
from analyze_results import generate_report

def main():
    parser = argparse.ArgumentParser(description='完整的图片数字识别处理')
    parser.add_argument('--input_dir', 
                        default='/Users/<USER>/Desktop/数据整合/视频/IMG_7_25',
                        help='输入图片目录')
    parser.add_argument('--output_prefix', 
                        default='full_ocr_results',
                        help='输出文件前缀')
    parser.add_argument('--batch_size', 
                        type=int, 
                        default=1000,
                        help='每批处理的图片数量')
    parser.add_argument('--start_from', 
                        type=int, 
                        default=0,
                        help='从第几张图片开始处理')
    parser.add_argument('--max_images', 
                        type=int, 
                        default=None,
                        help='最多处理多少张图片')
    
    args = parser.parse_args()
    
    print("=== 图片数字识别完整处理流程 ===")
    print(f"输入目录: {args.input_dir}")
    print(f"输出前缀: {args.output_prefix}")
    print(f"批处理大小: {args.batch_size}")
    
    # 检查输入目录
    if not os.path.exists(args.input_dir):
        print(f"错误: 输入目录不存在 - {args.input_dir}")
        return 1
    
    # 获取所有jpg文件
    jpg_files = [f for f in os.listdir(args.input_dir) if f.lower().endswith('.jpg')]
    jpg_files.sort()
    
    total_files = len(jpg_files)
    print(f"发现 {total_files} 张图片")
    
    if args.max_images:
        end_index = min(args.start_from + args.max_images, total_files)
    else:
        end_index = total_files
    
    actual_count = end_index - args.start_from
    print(f"将处理第 {args.start_from} 到第 {end_index-1} 张图片，共 {actual_count} 张")
    
    # 确认是否继续
    if actual_count > 100:
        response = input(f"即将处理 {actual_count} 张图片，这可能需要较长时间。是否继续？(y/N): ")
        if response.lower() != 'y':
            print("处理已取消")
            return 0
    
    # 开始处理
    start_time = time.time()
    csv_output = f"{args.output_prefix}.csv"
    
    print(f"\n开始批量处理...")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 批量处理图片
        batch_process_images(
            input_dir=args.input_dir,
            output_file=csv_output,
            start_index=args.start_from,
            end_index=end_index
        )
        
        # 生成分析报告
        print(f"\n生成分析报告...")
        report_output = f"{args.output_prefix}_report.txt"
        generate_report(csv_output, report_output)
        
        # 计算处理时间
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n=== 处理完成 ===")
        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总处理时间: {processing_time/60:.1f} 分钟")
        print(f"平均每张图片: {processing_time/actual_count:.2f} 秒")
        
        print(f"\n生成的文件:")
        print(f"  识别结果: {csv_output}")
        print(f"  分析报告: {report_output}")
        print(f"  可视化图表: {args.output_prefix}_analysis_charts.png")
        
        return 0
        
    except KeyboardInterrupt:
        print(f"\n处理被用户中断")
        return 1
    except Exception as e:
        print(f"\n处理过程中出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
