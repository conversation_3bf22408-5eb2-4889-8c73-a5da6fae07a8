# import cv2 
# import os 
 
# def extract_frames(video_path, output_dir, interval=3):
#     # 创建保存目录 
#     os.makedirs(output_dir,  exist_ok=True)
    
#     # 打开视频文件 
#     cap = cv2.VideoCapture(video_path)
#     if not cap.isOpened(): 
#         print("无法打开视频文件")
#         return 
    
#     # 初始化计数器 
#     frame_count = 0 
#     save_count = 0 
    
#     while True:
#         ret, frame = cap.read() 
#         if not ret:
#             break  # 视频读取完毕 
        
#         # 每3帧保存一次 
#         if frame_count % interval == 0:
#             # 生成6位数编号（如000001.jpg ）
#             save_path = os.path.join(output_dir,  f"{save_count:06d}.jpg")
#             cv2.imwrite(save_path,  frame)
#             save_count += 1 
        
#         frame_count += 1 
    
#     # 释放资源 
#     cap.release() 
#     print(f"共提取 {save_count} 张图片，保存至：{output_dir}")
 
# # 使用示例 
# extract_frames(
#     video_path="/Users/<USER>/Desktop/数据整合/视频/1354_1753410806.mp4",   # 替换为你的视频路径 
#     output_dir="/Users/<USER>/Desktop/数据整合/视频/IMG_7_25",  # 图片保存目录 
#     interval=24  # 抽帧间隔 
# )


# 视频处理帧
import cv2
import os

def extract_frames(video_path, output_dir):
    os.makedirs(output_dir, exist_ok=True)
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("无法打开视频文件")
        return
    
    # 获取视频的实际帧率（FPS）
    fps = cap.get(cv2.CAP_PROP_FPS)
    print(f"视频帧率: {fps:.2f} FPS")
    interval = int(round(fps))  # 直接根据FPS设置间隔
    
    frame_count = 0
    save_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # 按FPS间隔抽帧
        if frame_count % interval == 0:
            save_path = os.path.join(output_dir, f"{save_count:06d}.jpg")
            cv2.imwrite(save_path, frame)
            save_count += 1
        
        frame_count += 1
    
    cap.release()
    print(f"共提取 {save_count} 张图片（预期约399张），保存至：{output_dir}")

# 使用示例（无需手动指定interval）
extract_frames(
    video_path="/Users/<USER>/Desktop/数据整合/视频/1355_1753410840.mp4",
    output_dir="/Users/<USER>/Desktop/数据整合/视频/7_26"
)
