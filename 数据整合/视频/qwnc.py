import cv2
import numpy as np
from PIL import Image
import pytesseract
import os
import re

# 配置 Tesseract 路径（根据你的系统调整）
pytesseract.pytesseract.tesseract_cmd = '/opt/homebrew/bin/tesseract'  # macOS 示例路径

def preprocess_image(image_path):
    """
    对图像进行预处理，以便更好地识别数字。
    """
    # 读取图像
    image = cv2.imread(image_path)
    
    # 转换为灰度图像
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 应用高斯模糊以去除噪声
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # 应用自适应阈值化
    thresh = cv2.adaptiveThreshold(
        blurred,
        255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY_INV,
        11,  # 邻域大小
        2   # 常数 C
    )
    
    # 膨胀操作以增强数字的连通性
    kernel = np.ones((2, 2), np.uint8)
    dilated = cv2.dilate(thresh, kernel, iterations=1)
    
    return dilated

def recognize_digits(image_path):
    """
    使用 OCR 识别图像中的数字。
    """
    # 预处理图像
    preprocessed_image = preprocess_image(image_path)

    # 将 NumPy 数组转换为 PIL 图像
    pil_image = Image.fromarray(preprocessed_image)

    # 使用 Tesseract 进行 OCR 识别
    digits = pytesseract.image_to_string(
        pil_image,
        config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789.'
    )

    # 去除多余的空格和换行符
    digits = digits.strip()

    return digits

def enhanced_digit_recognition(image_path):
    """
    增强的数字识别函数，尝试多种方法提取数字
    """
    if not os.path.exists(image_path):
        return None

    # 读取原始图像
    image = cv2.imread(image_path)
    if image is None:
        return None

    # 尝试多种预处理方法
    methods = [
        preprocess_image,
        preprocess_for_digits_v2,
        preprocess_for_digits_v3
    ]

    # OCR配置选项
    configs = [
        '--psm 8 --oem 3 -c tessedit_char_whitelist=0123456789.',
        '--psm 7 --oem 3 -c tessedit_char_whitelist=0123456789.',
        '--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789.',
        '--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789.',
        '--psm 6 --oem 3'
    ]

    best_result = ""
    best_confidence = 0

    for method in methods:
        try:
            processed_image = method(image_path)
            pil_image = Image.fromarray(processed_image)

            for config in configs:
                try:
                    result = pytesseract.image_to_string(pil_image, config=config).strip()
                    # 提取数字
                    numbers = re.findall(r'\d+\.?\d*', result)
                    if numbers:
                        clean_result = ' '.join(numbers)
                        if len(clean_result) > len(best_result):
                            best_result = clean_result
                except:
                    continue
        except:
            continue

    return best_result if best_result else None

def preprocess_for_digits_v2(image_path):
    """
    第二种预处理方法，专门针对视频截图中的数字
    """
    image = cv2.imread(image_path)

    # 转换为灰度
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 增强对比度
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)

    # 使用OTSU阈值化
    _, thresh = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # 形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
    cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

    return cleaned

def preprocess_for_digits_v3(image_path):
    """
    第三种预处理方法，使用边缘检测
    """
    image = cv2.imread(image_path)

    # 转换为灰度
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 高斯模糊
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)

    # 边缘检测
    edges = cv2.Canny(blurred, 50, 150)

    # 膨胀操作连接边缘
    kernel = np.ones((2, 2), np.uint8)
    dilated = cv2.dilate(edges, kernel, iterations=1)

    return dilated

# 测试代码
if __name__ == "__main__":
    # 测试多张图片
    image_paths = [
        "/Users/<USER>/Desktop/数据整合/视频/IMG_7_25/000001.jpg",
        "/Users/<USER>/Desktop/数据整合/视频/IMG_7_25/000100.jpg",
        "/Users/<USER>/Desktop/数据整合/视频/IMG_7_25/001000.jpg",
        "/Users/<USER>/Desktop/数据整合/视频/IMG_7_25/002000.jpg",
        "/Users/<USER>/Desktop/数据整合/视频/IMG_7_25/002988.jpg"
    ]

    print("=== 使用增强识别方法测试 ===")
    # 逐个识别图片中的数字
    for i, image_path in enumerate(image_paths, start=1):
        try:
            print(f"\n正在处理图片 {i}: {os.path.basename(image_path)}")

            # 使用增强识别方法
            result = enhanced_digit_recognition(image_path)
            if result:
                print(f"✓ 识别结果: {result}")
            else:
                print("✗ 未识别到数字")

        except Exception as e:
            print(f"✗ 识别图片 {i} 时出错: {e}")

    print("\n=== 批量处理前10张图片 ===")
    # 批量处理前10张图片
    base_dir = "/Users/<USER>/Desktop/数据整合/视频/IMG_7_25"
    successful_count = 0

    for i in range(10):
        filename = f"{i:06d}.jpg"
        image_path = os.path.join(base_dir, filename)

        if os.path.exists(image_path):
            result = enhanced_digit_recognition(image_path)
            if result:
                print(f"{filename}: {result}")
                successful_count += 1
            else:
                print(f"{filename}: 未识别到数字")
        else:
            print(f"{filename}: 文件不存在")

    print(f"\n成功识别: {successful_count}/10")