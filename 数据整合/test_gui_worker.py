#!/usr/bin/env python3
"""
测试GUI版本的MergeWorker
"""
import sys
import os
sys.path.append('.')

from csv_merger import MergeWorker

def test_gui_worker():
    """测试GUI版本的MergeWorker"""
    print("=== 测试GUI版本的MergeWorker ===")
    
    # 测试文件
    file_paths = [
        "2025-07-24-WT1.csv",
        "2025-07-24-WT2.csv", 
        "2025-07-24-WT3.csv"
    ]
    
    output_path = "test_gui_output.csv"
    
    # 创建worker
    worker = MergeWorker(file_paths, output_path)
    
    # 连接信号来捕获输出
    def on_progress(value):
        print(f"进度: {value}%")
    
    def on_finished(message):
        print(f"完成: {message}")
    
    def on_error(error):
        print(f"错误: {error}")
    
    worker.progress.connect(on_progress)
    worker.finished.connect(on_finished)
    worker.error.connect(on_error)
    
    # 运行worker
    try:
        print("开始运行MergeWorker...")
        worker.run()
        
        # 检查输出文件
        if os.path.exists(output_path):
            print(f"✓ 输出文件生成成功: {output_path}")
            
            # 读取并显示前几行
            import pandas as pd
            df = pd.read_csv(output_path)
            print(f"✓ 文件包含 {len(df)} 行数据")
            print("前5行数据:")
            print(df.head())
        else:
            print("✗ 输出文件未生成")
            
    except Exception as e:
        print(f"✗ 运行失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gui_worker()
