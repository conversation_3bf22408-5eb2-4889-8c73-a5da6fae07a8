#!/usr/bin/env python3
"""
测试关键数据点提取功能
"""
import pandas as pd
import numpy as np
from pathlib import Path

def detect_time_column(df):
    """检测时间列名"""
    possible_time_columns = ['时间', 'Date_Time', 'Time', 'DateTime', 'Timestamp']
    for col in possible_time_columns:
        if col in df.columns:
            return col
    return df.columns[0]

def detect_data_columns(df, time_col):
    """检测数据列（X, Y, Z轴数据）"""
    data_cols = {}
    remaining_cols = [col for col in df.columns if col != time_col]

    # 查找X, Y, Z轴数据列
    x_cols = [col for col in remaining_cols if 'X' in col.upper() or 'x' in col]
    y_cols = [col for col in remaining_cols if 'Y' in col.upper() or 'y' in col]
    z_cols = [col for col in remaining_cols if 'Z' in col.upper() or 'z' in col]

    # 如果找到了XYZ列
    if x_cols and y_cols and z_cols:
        data_cols['X'] = x_cols[0]
        data_cols['Y'] = y_cols[0]
        data_cols['Z'] = z_cols[0]
    else:
        # 否则使用前三个数值列
        numeric_cols = []
        for col in remaining_cols:
            try:
                pd.to_numeric(df[col], errors='raise')
                numeric_cols.append(col)
            except:
                continue

        if len(numeric_cols) >= 3:
            data_cols['X'] = numeric_cols[0]
            data_cols['Y'] = numeric_cols[1]
            data_cols['Z'] = numeric_cols[2]

    return data_cols

def extract_key_data_points(df, time_col, data_cols):
    """
    按时间间隔处理数据，每秒提取中间点作为关键数据点
    确保数据的专业性和准确性
    """
    if df.empty:
        return df
    
    # 确保时间列是字符串格式
    df[time_col] = df[time_col].astype(str)
    
    # 按秒分组（只取时分秒，忽略毫秒）
    df['time_second'] = df[time_col].str[:8]  # 取前8位：HH:MM:SS
    
    # 按秒分组
    grouped = df.groupby('time_second')
    
    key_points = []
    for time_sec, group in grouped:
        if len(group) == 1:
            # 如果该秒只有一个数据点，直接使用
            key_points.append(group.iloc[0])
        else:
            # 如果该秒有多个数据点，选择中间点
            # 按时间戳排序，选择中间位置的数据点
            group_sorted = group.sort_values(time_col)
            middle_idx = len(group_sorted) // 2
            
            # 为了确保数据的专业性，我们选择最接近中位数的数据点
            # 而不是简单的中间位置
            if len(data_cols) >= 3:
                # 计算每个数据点的综合指标（使用欧几里得距离）
                x_col, y_col, z_col = data_cols['X'], data_cols['Y'], data_cols['Z']
                
                # 计算该秒内所有数据点的中位数
                x_median = group_sorted[x_col].median()
                y_median = group_sorted[y_col].median()
                z_median = group_sorted[z_col].median()
                
                # 计算每个点到中位数的距离
                distances = []
                for idx, row in group_sorted.iterrows():
                    dist = np.sqrt((row[x_col] - x_median)**2 + 
                                 (row[y_col] - y_median)**2 + 
                                 (row[z_col] - z_median)**2)
                    distances.append((dist, idx))
                
                # 选择距离中位数最近的点作为关键数据点
                distances.sort()
                best_idx = distances[0][1]
                key_points.append(group_sorted.loc[best_idx])
            else:
                # 如果没有足够的数据列，使用中间位置的点
                key_points.append(group_sorted.iloc[middle_idx])
    
    # 创建新的DataFrame
    result_df = pd.DataFrame(key_points)
    result_df = result_df.drop('time_second', axis=1, errors='ignore')
    
    # 按时间排序
    result_df = result_df.sort_values(time_col).reset_index(drop=True)
    
    return result_df

def test_key_points_extraction():
    """测试关键数据点提取功能"""
    print("=== 测试关键数据点提取功能 ===")
    
    # 测试文件
    test_file = "/Users/<USER>/Desktop/数据整理/1deal/数据整合/2025-07-24-WT6.csv"
    
    if not Path(test_file).exists():
        print(f"测试文件 {test_file} 不存在")
        return
    
    # 读取原始数据
    df_original = pd.read_csv(test_file)
    print(f"原始数据：{len(df_original)} 行")
    print(f"列名：{list(df_original.columns)}")
    
    # 检测时间列和数据列
    time_col = detect_time_column(df_original)
    data_cols = detect_data_columns(df_original, time_col)
    
    print(f"时间列：{time_col}")
    print(f"数据列：{data_cols}")
    
    # 清理数据
    df_clean = df_original.dropna(subset=[time_col])
    df_clean = df_clean[df_clean[time_col].astype(str).str.strip() != '']
    print(f"清理后数据：{len(df_clean)} 行")
    
    # 提取关键数据点
    df_key = extract_key_data_points(df_clean, time_col, data_cols)
    print(f"关键数据点：{len(df_key)} 行")
    
    # 显示前几行关键数据点
    print("\n前10个关键数据点：")
    print(df_key.head(10))
    
    # 保存关键数据点到文件
    output_file = "test_key_points_output.csv"
    df_key.to_csv(output_file, index=False)
    print(f"\n关键数据点已保存到：{output_file}")
    
    # 统计信息
    print(f"\n统计信息：")
    print(f"数据压缩比：{len(df_key)}/{len(df_clean)} = {len(df_key)/len(df_clean):.3f}")
    print(f"平均每秒数据点数：{len(df_clean)/len(df_key):.1f}")

if __name__ == "__main__":
    test_key_points_extraction()
