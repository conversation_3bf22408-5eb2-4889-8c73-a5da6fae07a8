#!/usr/bin/env python3
"""
CSV合并工具 - 命令行版本
优化版：按时间间隔处理数据，每秒提取关键数据点
"""
import pandas as pd
import numpy as np
from pathlib import Path
import argparse
import sys

def detect_time_column(df):
    """检测时间列名"""
    possible_time_columns = ['时间', 'Date_Time', 'Time', 'DateTime', 'Timestamp']
    for col in possible_time_columns:
        if col in df.columns:
            return col
    return df.columns[0]

def detect_data_columns(df, time_col):
    """检测数据列（X, Y, Z轴数据）"""
    data_cols = {}
    remaining_cols = [col for col in df.columns if col != time_col]

    # 查找X, Y, Z轴数据列
    x_cols = [col for col in remaining_cols if 'X' in col.upper() or 'x' in col]
    y_cols = [col for col in remaining_cols if 'Y' in col.upper() or 'y' in col]
    z_cols = [col for col in remaining_cols if 'Z' in col.upper() or 'z' in col]

    # 如果找到了XYZ列
    if x_cols and y_cols and z_cols:
        data_cols['X'] = x_cols[0]
        data_cols['Y'] = y_cols[0]
        data_cols['Z'] = z_cols[0]
    else:
        # 否则使用前三个数值列
        numeric_cols = []
        for col in remaining_cols:
            try:
                pd.to_numeric(df[col], errors='raise')
                numeric_cols.append(col)
            except:
                continue

        if len(numeric_cols) >= 3:
            data_cols['X'] = numeric_cols[0]
            data_cols['Y'] = numeric_cols[1]
            data_cols['Z'] = numeric_cols[2]

    return data_cols

def extract_key_data_points(df, time_col, data_cols):
    """
    按时间间隔处理数据，每秒提取中间点作为关键数据点
    确保数据的专业性和准确性
    """
    if df.empty:
        return df
    
    # 确保时间列是字符串格式
    df[time_col] = df[time_col].astype(str)
    
    # 按秒分组（只取时分秒，忽略毫秒）
    df['time_second'] = df[time_col].str[:8]  # 取前8位：HH:MM:SS
    
    # 按秒分组
    grouped = df.groupby('time_second')
    
    key_points = []
    for time_sec, group in grouped:
        if len(group) == 1:
            # 如果该秒只有一个数据点，直接使用
            key_points.append(group.iloc[0])
        else:
            # 如果该秒有多个数据点，选择中间点
            group_sorted = group.sort_values(time_col)
            
            # 为了确保数据的专业性，我们选择最接近中位数的数据点
            if len(data_cols) >= 3:
                # 计算每个数据点的综合指标（使用欧几里得距离）
                x_col, y_col, z_col = data_cols['X'], data_cols['Y'], data_cols['Z']
                
                # 计算该秒内所有数据点的中位数
                x_median = group_sorted[x_col].median()
                y_median = group_sorted[y_col].median()
                z_median = group_sorted[z_col].median()
                
                # 计算每个点到中位数的距离
                distances = []
                for idx, row in group_sorted.iterrows():
                    dist = np.sqrt((row[x_col] - x_median)**2 + 
                                 (row[y_col] - y_median)**2 + 
                                 (row[z_col] - z_median)**2)
                    distances.append((dist, idx))
                
                # 选择距离中位数最近的点作为关键数据点
                distances.sort()
                best_idx = distances[0][1]
                key_points.append(group_sorted.loc[best_idx])
            else:
                # 如果没有足够的数据列，使用中间位置的点
                middle_idx = len(group_sorted) // 2
                key_points.append(group_sorted.iloc[middle_idx])
    
    # 创建新的DataFrame
    result_df = pd.DataFrame(key_points)
    result_df = result_df.drop('time_second', axis=1, errors='ignore')
    
    # 按时间排序
    result_df = result_df.sort_values(time_col).reset_index(drop=True)
    
    return result_df

def merge_csv_files(file_paths, output_path):
    """合并CSV文件"""
    print(f"开始合并 {len(file_paths)} 个文件...")
    
    all_data = {}
    all_times = set()
    file_info = {}

    # 读取所有CSV文件并检测格式
    for i, file_path in enumerate(file_paths):
        print(f"处理文件 {i+1}/{len(file_paths)}: {Path(file_path).name}")
        
        df = pd.read_csv(file_path)
        file_name = Path(file_path).stem

        # 检测时间列和数据列
        time_col = detect_time_column(df)
        data_cols = detect_data_columns(df, time_col)

        if not data_cols:
            raise ValueError(f"文件 {file_name} 中未找到有效的数据列")

        print(f"  原始数据: {len(df)} 行")
        print(f"  时间列: {time_col}")
        print(f"  数据列: {data_cols}")

        # 清理数据：移除空行和无效数据
        df_clean = df.dropna(subset=[time_col])
        df_clean = df_clean[df_clean[time_col].astype(str).str.strip() != '']

        # 关键步骤：提取每秒的关键数据点（中间点）
        df_key_points = extract_key_data_points(df_clean, time_col, data_cols)
        
        if df_key_points.empty:
            raise ValueError(f"文件 {file_name} 处理后没有有效数据")

        print(f"  关键数据点: {len(df_key_points)} 行")

        # 收集所有时间点（现在是每秒一个关键点）
        all_times.update(df_key_points[time_col].values)

        # 存储文件信息
        file_info[file_name] = {
            'time_col': time_col,
            'data_cols': data_cols
        }

        # 存储每个文件的关键数据点
        all_data[file_name] = df_key_points.set_index(time_col)

    # 创建完整的时间索引
    sorted_times = sorted(all_times)
    print(f"总时间点数: {len(sorted_times)}")

    # 创建合并后的DataFrame，按照华300.csv的格式
    print("生成合并数据...")
    merged_data = []
    for j, time_point in enumerate(sorted_times):
        if j % 100 == 0:
            print(f"  进度: {j}/{len(sorted_times)}")
            
        # 创建符合目标格式的行
        row = {'时间': time_point, 'Tension': ''}  # Tension暂时为空

        # 按WT1, WT2, WT3...的顺序添加传感器数据
        wt_index = 1
        for file_name in sorted(all_data.keys()):
            info = file_info[file_name]

            if time_point in all_data[file_name].index:
                data = all_data[file_name].loc[time_point]

                # 处理重复时间点的情况：如果返回DataFrame，取第一行
                if isinstance(data, pd.DataFrame):
                    data = data.iloc[0]

                # 获取X, Y, Z数据，确保是标量值
                x_val = data[info['data_cols']['X']] if 'X' in info['data_cols'] else 0
                y_val = data[info['data_cols']['Y']] if 'Y' in info['data_cols'] else 0
                z_val = data[info['data_cols']['Z']] if 'Z' in info['data_cols'] else 0
            else:
                x_val = y_val = z_val = 0

            # 添加到行数据中，使用WT命名格式
            row[f'WT{wt_index}_X'] = x_val
            row[f'WT{wt_index}_Y'] = y_val
            row[f'WT{wt_index}_Z'] = z_val
            wt_index += 1

        merged_data.append(row)

    # 创建DataFrame并保存
    result_df = pd.DataFrame(merged_data)

    # 确保列的顺序正确：时间, Tension, WT1_X, WT1_Y, WT1_Z, WT2_X, WT2_Y, WT2_Z, ...
    ordered_columns = ['时间', 'Tension']
    wt_columns = []
    for i in range(1, len(file_paths) + 1):
        wt_columns.extend([f'WT{i}_X', f'WT{i}_Y', f'WT{i}_Z'])
    ordered_columns.extend(wt_columns)

    # 重新排列列顺序
    result_df = result_df.reindex(columns=ordered_columns, fill_value='')

    # 保存到CSV文件
    result_df.to_csv(output_path, index=False)
    
    print(f"\n✓ 成功合并 {len(file_paths)} 个传感器文件")
    print(f"✓ 智能提取 {len(sorted_times)} 个关键数据点（每秒一个）")
    print(f"✓ 输出格式：时间 + Tension(空) + {len(file_paths)}个传感器的XYZ数据")
    print(f"✓ 数据已按时间顺序排列，确保专业性和准确性")
    print(f"✓ 输出文件：{output_path}")

def main():
    parser = argparse.ArgumentParser(description='CSV传感器数据合并工具（优化版）')
    parser.add_argument('files', nargs='+', help='要合并的CSV文件')
    parser.add_argument('-o', '--output', default='华300.csv', help='输出文件名（默认：华300.csv）')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    for file_path in args.files:
        if not Path(file_path).exists():
            print(f"错误：文件 {file_path} 不存在")
            sys.exit(1)
    
    try:
        merge_csv_files(args.files, args.output)
    except Exception as e:
        print(f"错误：{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
