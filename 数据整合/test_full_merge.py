#!/usr/bin/env python3
"""
测试完整的CSV合并功能
"""
import pandas as pd
import numpy as np
from pathlib import Path
import sys

# 导入csv_merger中的MergeWorker类
sys.path.append('.')

def test_full_merge():
    """测试完整的CSV合并功能"""
    print("=== 测试完整的CSV合并功能 ===")
    
    # 测试文件列表
    test_files = [
        "2025-07-24-WT1.csv",
        "2025-07-24-WT2.csv", 
        "2025-07-24-WT3.csv",
        "2025-07-24-WT4.csv",
        "2025-07-24-WT5.csv",
        "2025-07-24-WT6.csv"
    ]
    
    # 检查文件是否存在
    existing_files = []
    for file in test_files:
        if Path(file).exists():
            existing_files.append(file)
            print(f"✓ 找到文件：{file}")
        else:
            print(f"✗ 文件不存在：{file}")
    
    if len(existing_files) < 2:
        print("需要至少2个文件进行测试")
        return
    
    print(f"\n将使用 {len(existing_files)} 个文件进行合并测试")
    
    # 手动实现合并逻辑（简化版）
    from csv_merger import MergeWorker
    
    output_path = "test_merged_output.csv"
    
    # 创建worker并运行
    worker = MergeWorker(existing_files, output_path)
    
    try:
        # 手动调用run方法
        worker.run()
        print(f"✓ 合并完成，输出文件：{output_path}")
        
        # 检查输出文件
        if Path(output_path).exists():
            df_result = pd.read_csv(output_path)
            print(f"✓ 输出文件包含 {len(df_result)} 行数据")
            print(f"✓ 列名：{list(df_result.columns)}")
            
            # 显示前几行
            print("\n前5行数据：")
            print(df_result.head())
            
            # 检查数据质量
            print(f"\n数据质量检查：")
            print(f"- 时间列非空行数：{df_result['时间'].notna().sum()}")
            print(f"- 数据列非零行数：{(df_result.iloc[:, 2:] != 0).any(axis=1).sum()}")
            
        else:
            print("✗ 输出文件未生成")
            
    except Exception as e:
        print(f"✗ 合并失败：{str(e)}")

if __name__ == "__main__":
    test_full_merge()
