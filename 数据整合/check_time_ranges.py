#!/usr/bin/env python3
"""
检查各个文件的时间范围
"""
import pandas as pd
from pathlib import Path

def check_time_ranges():
    """检查各个文件的时间范围"""
    print("=== 检查各个文件的时间范围 ===")
    
    files = ["/Users/<USER>/Desktop/数据整理/1deal/数据整合/2025-07-24-WT5.csv", "2025-07-24-WT2.csv", "2025-07-24-WT3.csv"]
    
    for file in files:
        if Path(file).exists():
            df = pd.read_csv(file)
            time_col = 'Date_Time'
            
            print(f"\n文件: {file}")
            print(f"总行数: {len(df)}")
            print(f"时间范围: {df[time_col].min()} - {df[time_col].max()}")
            
            # 显示前几个和后几个时间点
            print(f"前5个时间点: {df[time_col].head().tolist()}")
            print(f"后5个时间点: {df[time_col].tail().tolist()}")
            
            # 检查每秒的数据点数量
            df['time_second'] = df[time_col].astype(str).str[:8]
            time_counts = df['time_second'].value_counts().head(10)
            print(f"前10秒的数据点数量:")
            for time_sec, count in time_counts.items():
                print(f"  {time_sec}: {count} 个数据点")

if __name__ == "__main__":
    check_time_ranges()
