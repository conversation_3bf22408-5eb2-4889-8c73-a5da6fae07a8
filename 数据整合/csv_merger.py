import sys
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout,
                               QHBoxLayout, QWidget, QPushButton, QListWidget,
                               QLabel, QFileDialog, QMessageBox, QProgressBar)
from PySide6.QtCore import Qt, QThread, Signal

class MergeWorker(QThread):
    progress = Signal(int)
    finished = Signal(str)
    error = Signal(str)

    def __init__(self, file_paths, output_path):
        super().__init__()
        self.file_paths = file_paths
        self.output_path = output_path

    def detect_time_column(self, df):
        """检测时间列名"""
        possible_time_columns = ['时间', 'Date_Time', 'Time', 'DateTime', 'Timestamp']
        for col in possible_time_columns:
            if col in df.columns:
                return col
        # 如果没有找到，使用第一列
        return df.columns[0]

    def detect_data_columns(self, df, time_col):
        """检测数据列（X, Y, Z轴数据）"""
        data_cols = {}
        remaining_cols = [col for col in df.columns if col != time_col]

        # 查找X, Y, Z轴数据列
        x_cols = [col for col in remaining_cols if 'X' in col.upper() or 'x' in col]
        y_cols = [col for col in remaining_cols if 'Y' in col.upper() or 'y' in col]
        z_cols = [col for col in remaining_cols if 'Z' in col.upper() or 'z' in col]

        # 如果找到了XYZ列
        if x_cols and y_cols and z_cols:
            data_cols['X'] = x_cols[0]
            data_cols['Y'] = y_cols[0]
            data_cols['Z'] = z_cols[0]
        else:
            # 否则使用前三个数值列
            numeric_cols = []
            for col in remaining_cols:
                try:
                    pd.to_numeric(df[col], errors='raise')
                    numeric_cols.append(col)
                except:
                    continue

            if len(numeric_cols) >= 3:
                data_cols['X'] = numeric_cols[0]
                data_cols['Y'] = numeric_cols[1]
                data_cols['Z'] = numeric_cols[2]

        return data_cols

    def extract_key_data_points(self, df, time_col, data_cols):
        """
        按时间间隔处理数据，每秒提取中间点作为关键数据点
        确保数据的专业性和准确性
        """
        if df.empty:
            return df

        # 确保时间列是字符串格式
        df[time_col] = df[time_col].astype(str)

        # 按秒分组（只取时分秒，忽略毫秒）
        df['time_second'] = df[time_col].str[:8]  # 取前8位：HH:MM:SS

        # 按秒分组
        grouped = df.groupby('time_second')

        key_points = []
        for time_sec, group in grouped:
            if len(group) == 1:
                # 如果该秒只有一个数据点，直接使用
                key_points.append(group.iloc[0])
            else:
                # 如果该秒有多个数据点，选择中间点
                # 按时间戳排序，选择中间位置的数据点
                group_sorted = group.sort_values(time_col)
                middle_idx = len(group_sorted) // 2

                # 为了确保数据的专业性，我们选择最接近中位数的数据点
                # 而不是简单的中间位置
                if len(data_cols) >= 3:
                    # 计算每个数据点的综合指标（使用欧几里得距离）
                    x_col, y_col, z_col = data_cols['X'], data_cols['Y'], data_cols['Z']

                    # 计算该秒内所有数据点的中位数
                    x_median = group_sorted[x_col].median()
                    y_median = group_sorted[y_col].median()
                    z_median = group_sorted[z_col].median()

                    # 计算每个点到中位数的距离
                    distances = []
                    for idx, row in group_sorted.iterrows():
                        dist = np.sqrt((row[x_col] - x_median)**2 +
                                     (row[y_col] - y_median)**2 +
                                     (row[z_col] - z_median)**2)
                        distances.append((dist, idx))

                    # 选择距离中位数最近的点作为关键数据点
                    distances.sort()
                    best_idx = distances[0][1]
                    key_points.append(group_sorted.loc[best_idx])
                else:
                    # 如果没有足够的数据列，使用中间位置的点
                    key_points.append(group_sorted.iloc[middle_idx])

        # 创建新的DataFrame
        result_df = pd.DataFrame(key_points)
        result_df = result_df.drop('time_second', axis=1, errors='ignore')

        # 按时间排序
        result_df = result_df.sort_values(time_col).reset_index(drop=True)

        return result_df

    def run(self):
        try:
            all_data = {}
            all_times = set()
            file_info = {}

            # 读取所有CSV文件并检测格式
            for i, file_path in enumerate(self.file_paths):
                df = pd.read_csv(file_path)
                file_name = Path(file_path).stem

                # 检测时间列和数据列
                time_col = self.detect_time_column(df)
                data_cols = self.detect_data_columns(df, time_col)

                if not data_cols:
                    raise ValueError(f"文件 {file_name} 中未找到有效的数据列")

                # 清理数据：移除空行和无效数据
                df_clean = df.dropna(subset=[time_col])
                df_clean = df_clean[df_clean[time_col].astype(str).str.strip() != '']

                # 关键步骤：提取每秒的关键数据点（中间点）
                df_key_points = self.extract_key_data_points(df_clean, time_col, data_cols)

                if df_key_points.empty:
                    raise ValueError(f"文件 {file_name} 处理后没有有效数据")

                # 收集所有时间点（现在是每秒一个关键点）
                all_times.update(df_key_points[time_col].values)

                # 存储文件信息
                file_info[file_name] = {
                    'time_col': time_col,
                    'data_cols': data_cols
                }

                # 存储每个文件的关键数据点
                all_data[file_name] = df_key_points.set_index(time_col)

                self.progress.emit(int((i + 1) / len(self.file_paths) * 50))

            # 创建完整的时间索引
            sorted_times = sorted(all_times)

            # 创建合并后的DataFrame，按照华200.csv的格式
            merged_data = []
            for j, time_point in enumerate(sorted_times):
                # 创建符合目标格式的行
                row = {'时间': time_point, 'Tension': ''}  # Tension暂时为空

                # 按WT1, WT2, WT3...的顺序添加传感器数据
                wt_index = 1
                for file_name in sorted(all_data.keys()):
                    info = file_info[file_name]

                    if time_point in all_data[file_name].index:
                        data = all_data[file_name].loc[time_point]

                        # 处理重复时间点的情况：如果返回DataFrame，取第一行
                        if isinstance(data, pd.DataFrame):
                            data = data.iloc[0]

                        # 获取X, Y, Z数据，确保是标量值
                        x_val = data[info['data_cols']['X']] if 'X' in info['data_cols'] else 0
                        y_val = data[info['data_cols']['Y']] if 'Y' in info['data_cols'] else 0
                        z_val = data[info['data_cols']['Z']] if 'Z' in info['data_cols'] else 0
                    else:
                        x_val = y_val = z_val = 0

                    # 添加到行数据中，使用WT命名格式
                    row[f'WT{wt_index}_X'] = x_val
                    row[f'WT{wt_index}_Y'] = y_val
                    row[f'WT{wt_index}_Z'] = z_val
                    wt_index += 1

                merged_data.append(row)
                self.progress.emit(50 + int((j + 1) / len(sorted_times) * 50))

            # 创建DataFrame并保存
            result_df = pd.DataFrame(merged_data)

            # 确保列的顺序正确：时间, Tension, WT1_X, WT1_Y, WT1_Z, WT2_X, WT2_Y, WT2_Z, ...
            ordered_columns = ['时间', 'Tension']
            wt_columns = []
            for i in range(1, len(self.file_paths) + 1):
                wt_columns.extend([f'WT{i}_X', f'WT{i}_Y', f'WT{i}_Z'])
            ordered_columns.extend(wt_columns)

            # 重新排列列顺序
            result_df = result_df.reindex(columns=ordered_columns, fill_value='')

            # 保存到CSV文件
            result_df.to_csv(self.output_path, index=False)

            self.finished.emit(f"✓ 成功合并 {len(self.file_paths)} 个传感器文件\n"
                               f"✓ 智能提取 {len(sorted_times)} 个关键数据点（每秒一个）\n"
                               f"✓ 输出格式：时间 + Tension(空) + {len(self.file_paths)}个传感器的XYZ数据\n"
                               f"✓ 数据已按时间顺序排列，确保专业性和准确性")

        except Exception as e:
            self.error.emit(f"合并失败: {str(e)}")

class CSVMergerWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.file_paths = []
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle("传感器数据整合工具 - 生成华300格式（优化版）")
        self.setGeometry(100, 100, 750, 550)

        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # 说明文本
        info_label = QLabel("说明：将多个传感器CSV文件合并为华300格式\n"
                           "✓ 智能提取每秒关键数据点（选择最接近中位数的数据点）\n"
                           "✓ 按时间顺序排列，确保数据的专业性和准确性\n"
                           "✓ 输出格式：时间 + Tension(空) + WT1_X,Y,Z + WT2_X,Y,Z + ...")
        info_label.setStyleSheet("color: #666; font-size: 12px; padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        layout.addWidget(info_label)

        # 文件选择区域
        file_layout = QHBoxLayout()
        self.add_files_btn = QPushButton("添加传感器CSV文件")
        self.clear_files_btn = QPushButton("清空列表")
        file_layout.addWidget(self.add_files_btn)
        file_layout.addWidget(self.clear_files_btn)
        layout.addLayout(file_layout)

        # 文件列表
        self.file_list = QListWidget()
        layout.addWidget(QLabel("已选择的传感器文件 (将按文件名顺序编号为WT1, WT2, ...):"))
        layout.addWidget(self.file_list)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # 合并按钮
        self.merge_btn = QPushButton("开始智能合并生成华300格式")
        self.merge_btn.setEnabled(False)
        layout.addWidget(self.merge_btn)

        # 连接信号
        self.add_files_btn.clicked.connect(self.add_files)
        self.clear_files_btn.clicked.connect(self.clear_files)
        self.merge_btn.clicked.connect(self.merge_files)
    
    def add_files(self):
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择传感器CSV文件", "", "CSV文件 (*.csv)")

        for file_path in files:
            if file_path not in self.file_paths:
                self.file_paths.append(file_path)
                # 显示文件名和将要分配的WT编号
                file_name = Path(file_path).name
                wt_number = len(self.file_paths)
                self.file_list.addItem(f"WT{wt_number}: {file_name}")

        self.merge_btn.setEnabled(len(self.file_paths) >= 1)  # 至少需要1个文件

    def clear_files(self):
        self.file_paths.clear()
        self.file_list.clear()
        self.merge_btn.setEnabled(False)

    def merge_files(self):
        if len(self.file_paths) < 1:
            QMessageBox.warning(self, "警告", "请至少选择1个传感器CSV文件")
            return

        # 建议默认文件名
        default_name = f"华{len(self.file_paths) * 100}.csv"
        output_path, _ = QFileDialog.getSaveFileName(
            self, "保存华300格式文件", default_name, "CSV文件 (*.csv)")

        if not output_path:
            return

        # 开始合并
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.merge_btn.setEnabled(False)

        self.worker = MergeWorker(self.file_paths, output_path)
        self.worker.progress.connect(self.progress_bar.setValue)
        self.worker.finished.connect(self.on_merge_finished)
        self.worker.error.connect(self.on_merge_error)
        self.worker.start()
    
    def on_merge_finished(self, message):
        self.progress_bar.setVisible(False)
        self.merge_btn.setEnabled(True)
        QMessageBox.information(self, "成功", message)
    
    def on_merge_error(self, error_message):
        self.progress_bar.setVisible(False)
        self.merge_btn.setEnabled(True)
        QMessageBox.critical(self, "错误", error_message)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = CSVMergerWindow()
    window.show()
    sys.exit(app.exec())