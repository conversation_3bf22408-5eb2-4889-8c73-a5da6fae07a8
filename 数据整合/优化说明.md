# CSV数据合并工具优化说明

## 优化内容

### 1. 关键数据点提取算法
- **问题**：原始数据每秒包含多个数据点（平均约20个），数据冗余严重
- **解决方案**：实现智能关键数据点提取算法
  - 按秒分组数据
  - 对于每秒的多个数据点，计算X、Y、Z轴的中位数
  - 选择距离中位数最近的数据点作为该秒的关键数据点
  - 确保每秒只保留一个最具代表性的数据点

### 2. 数据专业性和准确性保证
- **中位数算法**：使用欧几里得距离计算每个数据点到中位数的距离
- **最优选择**：选择距离最小的点，确保数据的代表性
- **时间排序**：所有数据按时间严格排序
- **数据完整性**：保持原始数据的精度，不进行平均化处理

### 3. 性能优化
- **数据压缩**：将原始数据压缩至约5%（每秒20个点→1个点）
- **处理速度**：大幅提升处理速度和内存效率
- **文件大小**：显著减少输出文件大小

## 测试结果

### 原始数据统计
- **WT1文件**：38,199行 → 1,926个关键点
- **WT2文件**：37,368行 → 1,883个关键点  
- **WT3文件**：38,173行 → 1,924个关键点
- **压缩比**：约0.05（95%的数据压缩）

### 时间范围处理
- **WT1**：19:08:56 - 19:44:39
- **WT2**：19:13:17 - 19:44:39（开始时间较晚）
- **WT3**：19:08:58 - 19:44:39
- **智能处理**：自动处理不同传感器的时间差异

### 输出格式
```csv
时间,Tension,WT1_X,WT1_Y,WT1_Z,WT2_X,WT2_Y,WT2_Z,WT3_X,WT3_Y,WT3_Z
19:08:56,,0.546387,0.188965,-0.848145,0.0,0.0,0.0,0.0,0.0,0.0
19:13:17,,0.063477,-0.187012,0.978516,-0.544922,0.414062,0.690918,-0.039551,0.037598,1.001465
```

## 文件说明

### 主要文件
1. **csv_merger.py** - 优化后的GUI版本
2. **csv_merger_cli.py** - 命令行版本
3. **test_华300_optimized.csv** - 优化后的输出示例

### 测试文件
1. **test_key_points.py** - 关键数据点提取测试
2. **debug_merge.py** - 合并功能调试
3. **check_time_ranges.py** - 时间范围检查

## 使用方法

### GUI版本
```bash
python csv_merger.py
```

### 命令行版本
```bash
python csv_merger_cli.py file1.csv file2.csv file3.csv -o output.csv
```

## 技术特点

### 1. 智能时间处理
- 自动检测时间列格式
- 支持多种时间格式（Date_Time, 时间, Time等）
- 按秒精度进行数据分组

### 2. 数据列自动识别
- 智能识别X、Y、Z轴数据列
- 支持多种命名格式（X_Acceler, X, x等）
- 自动处理数值列

### 3. 专业数据处理
- 使用统计学方法选择关键点
- 保持数据的物理意义
- 避免简单平均可能带来的失真

### 4. 错误处理
- 完善的异常处理机制
- 详细的进度显示
- 数据质量检查

## 优化效果

1. **数据质量**：保持了原始数据的特征，选择最具代表性的数据点
2. **处理效率**：大幅提升处理速度，减少内存占用
3. **文件大小**：输出文件大小减少95%
4. **专业性**：使用科学的统计方法确保数据准确性
5. **兼容性**：完全兼容原有的华300格式要求

## 总结

优化后的CSV合并工具不仅保持了原有功能，还显著提升了数据处理的专业性和效率。通过智能的关键数据点提取算法，确保了每秒一个高质量的数据点，既满足了数据分析的需求，又大幅提升了处理性能。
