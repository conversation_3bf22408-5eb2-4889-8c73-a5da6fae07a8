# TXT to CSV 转换器

这个Python脚本可以将特定格式的txt文件转换为CSV格式。

## 支持的输入格式

```
导出时间:2023-08-24 03:07:07
序 号        日  期       时  间            深  度            速  度                张  力            张力增量    
00001      2023-08-12    20:44:02            -19.61 m           154.6 m/h                0 KN              0 KN/s
00002      2023-08-12    20:44:03            -19.34 m           534.3 m/h             51.3 KN              0 KN/s
```

## 输出格式

生成的CSV文件包含以下列：
- 序号
- 日期
- 时间
- 深度
- 速度
- 张力
- 张力增量

导出时间会作为注释保存在CSV文件的第一行。

## 使用方法

### 命令行使用

```bash
# 基本用法（输出文件名自动生成）
python txt_to_csv_converter.py input_file.txt

# 指定输出文件名
python txt_to_csv_converter.py input_file.txt output_file.csv
```

### 在Python代码中使用

```python
from txt_to_csv_converter import convert_txt_to_csv

# 转换文件
success = convert_txt_to_csv('input_file.txt', 'output_file.csv')
if success:
    print("转换成功!")
else:
    print("转换失败!")
```

## 功能特点

1. **自动编码检测**: 支持UTF-8、GBK、GB2312编码
2. **灵活的数据解析**: 使用正则表达式和备用解析方法
3. **错误处理**: 提供详细的错误信息和状态反馈
4. **保留元数据**: 导出时间作为注释保存在CSV文件中

## 示例

运行示例：
```bash
python txt_to_csv_converter.py sample_data.txt
```

输出：
```
转换成功!
输入文件: sample_data.txt
输出文件: sample_data.csv
导出时间: 2023-08-24 03:07:07
数据行数: 4
```

生成的CSV文件内容：
```csv
# 导出时间: 2023-08-24 03:07:07
序号,日期,时间,深度,速度,张力,张力增量
00001,2023-08-12,20:44:02,-19.61 m,154.6 m/h,0 KN,0 KN/s
00002,2023-08-12,20:44:03,-19.34 m,534.3 m/h,51.3 KN,0 KN/s
00003,2023-08-12,20:44:04,-19.12 m,432.1 m/h,48.7 KN,1.2 KN/s
00004,2023-08-12,20:44:05,-18.89 m,398.5 m/h,52.1 KN,0.8 KN/s
```

## 注意事项

- 确保输入的txt文件格式符合预期
- 脚本会自动跳过表头行和空行
- 如果数据格式不标准，脚本会尝试使用备用解析方法
- 输出的CSV文件使用UTF-8编码
