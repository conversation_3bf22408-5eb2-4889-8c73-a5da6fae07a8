#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TXT to CSV Converter
将特定格式的txt文件转换为CSV格式

输入格式示例:
导出时间:2023-08-24 03:07:07
序 号        日  期       时  间            深  度            速  度                张  力            张力增量    
00001      2023-08-12    20:44:02            -19.61 m           154.6 m/h                0 KN              0 KN/s
00002      2023-08-12    20:44:03            -19.34 m           534.3 m/h             51.3 KN              0 KN/s
"""

import re
import csv
import os
import sys
from typing import List, Dict, Optional


def parse_txt_file(file_path: str) -> tuple[Optional[str], List[Dict[str, str]]]:
    """
    解析txt文件，提取导出时间和数据行
    
    Args:
        file_path: txt文件路径
        
    Returns:
        tuple: (导出时间, 数据行列表)
    """
    export_time = None
    data_rows = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except UnicodeDecodeError:
        # 尝试其他编码
        try:
            with open(file_path, 'r', encoding='gbk') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            with open(file_path, 'r', encoding='gb2312') as file:
                lines = file.readlines()
    
    for line in lines:
        line = line.strip()
        
        # 提取导出时间
        if line.startswith('导出时间:'):
            export_time = line.replace('导出时间:', '').strip()
            continue
            
        # 跳过表头行和空行
        if not line or '序 号' in line or line.startswith('-'):
            continue
            
        # 解析数据行
        # 使用正则表达式匹配数据行格式
        # 格式: 序号 日期 时间 深度 速度 张力 张力增量
        pattern = r'(\d+)\s+(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2}:\d{2})\s+([-\d.]+\s*m)\s+([\d.]+\s*m/h)\s+([\d.]+\s*KN)\s+([\d.]+\s*KN/s)'
        
        match = re.match(pattern, line)
        if match:
            row_data = {
                '序号': match.group(1),
                '日期': match.group(2),
                '时间': match.group(3),
                '深度': match.group(4).strip(),
                '速度': match.group(5).strip(),
                '张力': match.group(6).strip(),
                '张力增量': match.group(7).strip()
            }
            data_rows.append(row_data)
        else:
            # 如果正则匹配失败，尝试按空格分割
            parts = line.split()
            if len(parts) >= 7:
                row_data = {
                    '序号': parts[0],
                    '日期': parts[1],
                    '时间': parts[2],
                    '深度': ' '.join(parts[3:5]) if len(parts) > 4 and 'm' in parts[4] else parts[3],
                    '速度': ' '.join(parts[4:6]) if 'm/h' in ' '.join(parts[4:6]) else parts[4],
                    '张力': ' '.join(parts[5:7]) if 'KN' in ' '.join(parts[5:7]) else parts[5],
                    '张力增量': ' '.join(parts[6:8]) if 'KN/s' in ' '.join(parts[6:8]) else parts[6]
                }
                data_rows.append(row_data)
    
    return export_time, data_rows


def write_csv_file(output_path: str, export_time: Optional[str], data_rows: List[Dict[str, str]]):
    """
    将数据写入CSV文件
    
    Args:
        output_path: 输出CSV文件路径
        export_time: 导出时间
        data_rows: 数据行列表
    """
    if not data_rows:
        print("警告: 没有找到有效的数据行")
        return
    
    fieldnames = ['序号', '日期', '时间', '深度', '速度', '张力', '张力增量']
    
    with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        # 如果有导出时间，作为注释写入第一行
        if export_time:
            csvfile.write(f"# 导出时间: {export_time}\n")
        
        # 写入表头
        writer.writeheader()
        
        # 写入数据行
        writer.writerows(data_rows)


def convert_txt_to_csv(input_file: str, output_file: str = None):
    """
    转换txt文件为CSV格式
    
    Args:
        input_file: 输入的txt文件路径
        output_file: 输出的CSV文件路径，如果为None则自动生成
    """
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 '{input_file}' 不存在")
        return False
    
    # 如果没有指定输出文件，自动生成
    if output_file is None:
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}.csv"
    
    try:
        # 解析txt文件
        export_time, data_rows = parse_txt_file(input_file)
        
        if not data_rows:
            print(f"错误: 在文件 '{input_file}' 中没有找到有效的数据行")
            return False
        
        # 写入CSV文件
        write_csv_file(output_file, export_time, data_rows)
        
        print(f"转换成功!")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        print(f"导出时间: {export_time or '未找到'}")
        print(f"数据行数: {len(data_rows)}")
        
        return True
        
    except Exception as e:
        print(f"转换过程中发生错误: {str(e)}")
        return False


def main():
    """主函数，处理命令行参数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print(f"  python {sys.argv[0]} <输入txt文件> [输出csv文件]")
        print("\n示例:")
        print(f"  python {sys.argv[0]} data.txt")
        print(f"  python {sys.argv[0]} data.txt output.csv")
        return
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    convert_txt_to_csv(input_file, output_file)


if __name__ == "__main__":
    main()
